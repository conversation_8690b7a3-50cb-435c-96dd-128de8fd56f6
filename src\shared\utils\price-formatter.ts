/**
 * Utilitários centralizados para formatação e parsing de preços
 */

/**
 * Converte um valor monetário formatado (string) para número
 * @param value - Valor formatado como "R$ 1.234,56" ou "1234.56" ou number
 * @returns Número decimal
 */
export function parsePrice(value: string | number | undefined | null): number {
	if (typeof value === "number") return isNaN(value) ? 0 : value;
	if (!value || value === "") return 0;

	// Remove prefixos, pontos de milhares e converte vírgula para ponto decimal
	const cleanValue = value
		.toString()
		.replace(/R\$\s?/g, "")
		.replace(/\./g, "")
		.replace(",", ".")
		.trim();

	const numericValue = parseFloat(cleanValue);
	return isNaN(numericValue) ? 0 : numericValue;
}

/**
 * Formata um número para exibição monetária brasileira
 * @param value - Valor numérico
 * @returns String formatada como "R$ 1.234,56"
 */
export function formatPrice(value: number | string | undefined | null): string {
	const numericValue = typeof value === "string" ? parsePrice(value) : (value ?? 0);

	if (isNaN(numericValue)) return "R$ 0,00";

	return new Intl.NumberFormat("pt-BR", {
		style: "currency",
		currency: "BRL",
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(numericValue);
}

/**
 * Converte um número para valor adequado para uso em inputs NumericFormat
 * @param value - Valor numérico
 * @returns Valor numérico ou undefined para inputs NumericFormat
 */
export function formatPriceForInput(value: number | undefined | null): number | undefined {
	if (!value || isNaN(value)) return undefined;
	return value;
}

/**
 * Valida se um valor de preço é válido
 * @param value - Valor a ser validado
 * @returns true se válido, false caso contrário
 */
export function isValidPrice(value: string | number | undefined | null): boolean {
	const numericValue = parsePrice(value);
	return numericValue > 0;
}

/**
 * Calcula a margem de lucro entre preço de custo e preço de venda
 * @param costPrice - Preço de custo
 * @param salePrice - Preço de venda
 * @returns Margem em porcentagem
 */
export function calculateMargin(costPrice: number, salePrice: number): number {
	if (!costPrice || !salePrice || salePrice <= 0) return 0;
	const profit = salePrice - costPrice;
	return (profit / salePrice) * 100;
}

/**
 * Calcula o preço de venda baseado no custo e margem desejada
 * @param costPrice - Preço de custo
 * @param marginPercentage - Margem desejada em porcentagem
 * @returns Preço de venda calculado
 */
export function calculateSalePrice(costPrice: number, marginPercentage: number): number {
	if (!costPrice || marginPercentage === undefined || marginPercentage === 100) return 0;
	return costPrice / (1 - marginPercentage / 100);
}
