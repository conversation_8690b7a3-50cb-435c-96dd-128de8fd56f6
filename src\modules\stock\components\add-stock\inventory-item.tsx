import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { FormField } from "@/shared/components/ui/form";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Separator } from "@/shared/components/ui/separator";
import { BarcodeInput } from "@/shared/components/utils/barcode-input";
import { Box, HelpCircle, Package2, Trash2, Calculator } from "lucide-react";
import React, { useState } from "react";
import { Controller, UseFormReturn, useWatch, type FieldError } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { useCalculatedQuantity } from "../../hooks/quantity/calculate.hook";
import { ICreateStock } from "../../validators/create-stock.validator";
import { PricingCalculator } from "./pricing-calculator";

interface InventoryItemProps {
	index: number;
	methodsForm: UseFormReturn<ICreateStock>;
	removeItem: () => void;
	isExistingIdProduct: boolean;
	isExistingIdPackage: boolean;
}

export const InventoryItem: React.FC<InventoryItemProps> = ({
	index,
	methodsForm,
	removeItem,
	isExistingIdProduct = false,
	isExistingIdPackage = false,
}) => {
	const [showPricingCalculator, setShowPricingCalculator] = useState(false);
	const readonlyClass = "bg-gray-100 cursor-not-allowed text-gray-500";

	const quantityPerPackageValue = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.product.package.quantityPerPackage`,
	});

	const packageQuantity = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.packageQuantity`,
		defaultValue: undefined,
	});

	const currentCostPrice = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.product.costPrice`,
	});

	const fieldName = `inventories.${index}.stockMovement.quantity` as const;
	const { calculatedQuantity, handleQuantityChange } = useCalculatedQuantity({
		quantityPerPackageValue,
		packageQuantity,
		setValue: methodsForm.setValue,
		fieldName,
	});

	const getError = (path: string): FieldError | undefined => {
		const error = path.split(".").reduce(
			(acc: unknown, key: string) => {
				if (acc && typeof acc === "object" && key in acc) {
					return (acc as Record<string, unknown>)[key];
				}
				return undefined;
			},
			methodsForm.formState.errors as Record<string, unknown> | undefined
		);
		return error as FieldError | undefined;
	};

	const RequiredLabel = ({ children }: { children: React.ReactNode }) => (
		<span className="flex items-center">
			{children}
			<span className="text-red-500 ml-1">*</span>
		</span>
	);

	return (
		<div className="relative z-10 border border-gray-200 rounded-xl bg-gray-50 p-4 mb-3 transition-all hover:shadow-sm ">
			<div className="flex items-center justify-between mb-3">
				<h4 className="text-sm font-semibold text-gray-600 flex items-center space-x-1">
					<Box size={16} className="text-gray-500" />
					<span>Item {index + 1}</span>
				</h4>
				<button
					type="button"
					onClick={removeItem}
					className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-50"
					title="Remover este item"
				>
					<Trash2 size={16} />
				</button>
			</div>
			<p className="text-xs font-bold text-gray-500 flex items-center mb-2">
				<Package2 size={14} className="mr-1" />
				Detalhes do Produto
			</p>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-2">
				<div>
					<Label className="text-xs" htmlFor={`inventories.${index}.stockMovement.product.name`}>
						<RequiredLabel>Nome do Produto</RequiredLabel>
					</Label>
					<Input
						type="text"
						placeholder="Ex: Sabão em pó"
						readOnly={isExistingIdProduct}
						className={`text-sm ${isExistingIdProduct ? readonlyClass : ""} ${
							getError(`inventories.${index}.stockMovement.product.name`) ? "border-red-500" : ""
						}`}
						{...methodsForm.register(`inventories.${index}.stockMovement.product.name`, {
							required: "Nome do produto é obrigatório",
						})}
					/>
					{getError(`inventories.${index}.stockMovement.product.name`) && (
						<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.name`)?.message}</span>
					)}
				</div>

				<div>
					<Label className="text-xs flex items-center">
						<RequiredLabel>
							Código de Barras
							<div className="relative group inline-block">
								<HelpCircle className="ml-1 w-3 h-3 text-gray-400" />
								<span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 hidden w-max rounded bg-gray-700 p-1 text-xs text-white group-hover:block">
									Código de barras do produto
								</span>
							</div>
						</RequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.barcode`}
						control={methodsForm.control}
						rules={{
							required: "Código de barras é obrigatório",
							pattern: {
								value: /^[0-9]*$/,
								message: "Apenas números são permitidos",
							},
						}}
						render={({ field }) => (
							<BarcodeInput
								value={field.value || ""}
								onChange={field.onChange}
								readOnly={isExistingIdProduct}
								error={getError(`inventories.${index}.stockMovement.product.barcode`)?.message}
							/>
						)}
					/>
					{getError(`inventories.${index}.stockMovement.product.barcode`) && (
						<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.barcode`)?.message}</span>
					)}
				</div>
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
				<div className="flex flex-col md:flex-row gap-2">
					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>Preço de Custo</RequiredLabel>
						</Label>
						<Controller
							name={`inventories.${index}.stockMovement.product.costPrice`}
							control={methodsForm.control}
							rules={{
								required: "Preço de custo é obrigatório",
								min: {
									value: 0.01,
									message: "Preço de custo deve ser maior que zero",
								},
							}}
							render={({ field: { ref, ...rest } }) => (
								<NumericFormat
									thousandSeparator="."
									decimalSeparator=","
									prefix="R$ "
									decimalScale={2}
									fixedDecimalScale
									placeholder="R$ 0,00"
									readOnly={isExistingIdProduct}
									className={`text-sm mt-1 w-full px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-400 ${
										isExistingIdProduct ? readonlyClass : ""
									} ${getError(`inventories.${index}.stockMovement.product.costPrice`) ? "border-red-500" : "border-gray-300"}`}
									getInputRef={ref}
									{...rest}
								/>
							)}
						/>
						{getError(`inventories.${index}.stockMovement.product.costPrice`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.costPrice`)?.message}</span>
						)}
					</div>
					<div className="w-full md:w-1/2 relative">
						<Label className="text-xs">
							<RequiredLabel>Preço de Venda</RequiredLabel>
						</Label>
						<div className="relative">
							<Controller
								name={`inventories.${index}.stockMovement.product.price`}
								control={methodsForm.control}
								rules={{
									required: "Preço de venda é obrigatório",
									min: {
										value: 0.01,
										message: "Preço de venda deve ser maior que zero",
									},
								}}
								render={({ field: { ref, ...rest } }) => (
									<NumericFormat
										thousandSeparator="."
										decimalSeparator=","
										prefix="R$ "
										decimalScale={2}
										fixedDecimalScale
										placeholder="R$ 0,00"
										readOnly={isExistingIdProduct}
										className={`text-sm mt-1 w-full px-3 py-1.5 ${
											!isExistingIdProduct && currentCostPrice && currentCostPrice > 0 ? "pr-10" : ""
										} border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-400 ${
											isExistingIdProduct ? readonlyClass : ""
										} ${getError(`inventories.${index}.stockMovement.product.price`) ? "border-red-500" : "border-gray-300"}`}
										getInputRef={ref}
										{...rest}
									/>
								)}
							/>
							{!isExistingIdProduct && currentCostPrice && currentCostPrice > 0 && (
								<button
									type="button"
									onClick={() => setShowPricingCalculator(!showPricingCalculator)}
									className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-full transition-all duration-200 ${
										showPricingCalculator
											? "bg-mainColor text-white shadow-md"
											: "bg-gray-100 text-gray-500 hover:bg-mainColor/10 hover:text-mainColor"
									}`}
									title="Calculadora de preços"
								>
									<Calculator size={14} />
								</button>
							)}
						</div>
						{getError(`inventories.${index}.stockMovement.product.price`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.price`)?.message}</span>
						)}
						{showPricingCalculator && (
							<PricingCalculator
								index={index}
								methodsForm={methodsForm}
								isOpen={showPricingCalculator}
								onClose={() => setShowPricingCalculator(false)}
							/>
						)}
					</div>
				</div>
				<div className="flex flex-col md:flex-row gap-2">
					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>Quantidade</RequiredLabel>
						</Label>
						<Controller
							name={`inventories.${index}.stockMovement.quantity`}
							control={methodsForm.control}
							rules={{
								required: "Quantidade é obrigatória",
								min: {
									value: 1,
									message: "Quantidade deve ser maior que zero",
								},
							}}
							render={({ field: { ref } }) => (
								<Input
									type="number"
									inputMode="numeric"
									pattern="[0-9]*"
									placeholder="0"
									value={calculatedQuantity}
									onChange={handleQuantityChange}
									ref={ref}
									className={`text-sm  no-spinner  ${getError(`inventories.${index}.stockMovement.quantity`) ? "border-red-500" : ""}`}
								/>
							)}
						/>
						{getError(`inventories.${index}.stockMovement.quantity`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.quantity`)?.message}</span>
						)}
					</div>
					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>Código do produto</RequiredLabel>
						</Label>
						<Input
							type="text"
							placeholder="Ex: PROD-0001"
							readOnly={isExistingIdProduct}
							className={`text-sm ${isExistingIdProduct ? readonlyClass : ""} ${
								getError(`inventories.${index}.stockMovement.product.code`) ? "border-red-500" : ""
							}`}
							{...methodsForm.register(`inventories.${index}.stockMovement.product.code`, {
								required: "Código do produto é obrigatório",
							})}
						/>
						{getError(`inventories.${index}.stockMovement.product.code`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.code`)?.message}</span>
						)}
					</div>

					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>NCM</RequiredLabel>
						</Label>
						<Input
							type="text"
							placeholder="Ex: 1234.56.78"
							readOnly={isExistingIdProduct}
							className={`text-sm ${isExistingIdProduct ? readonlyClass : ""} ${
								getError(`inventories.${index}.stockMovement.product.ncm`) ? "border-red-500" : ""
							}`}
							{...methodsForm.register(`inventories.${index}.stockMovement.product.ncm`, {
								required: "NCM é obrigatório",
								pattern: {
									value: /^\d{4}\.\d{2}\.\d{2}$/,
									message: "Formato inválido. Use o formato: 1234.56.78",
								},
							})}
						/>
						{getError(`inventories.${index}.stockMovement.product.ncm`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.ncm`)?.message}</span>
						)}
					</div>
				</div>
				<div className="flex flex-col md:flex-row gap-2">
					<FormField
						name={`inventories.${index}.expirationDate`}
						control={methodsForm.control}
						rules={{
							required: "Data de validade é obrigatória",
						}}
						render={({ field }) => (
							<DatePickerInput
								className="w-full md:w-1/2"
								field={field}
								inputDateClassName={`text-sm ${isExistingIdProduct ? readonlyClass : ""} ${
									getError(`inventories.${index}.expirationDate`) ? "border-red-500" : ""
								}`}
								label="Validade"
								labelClassName="text-xs"
							/>
						)}
					/>
				</div>
			</div>
			<Separator className="my-4 bg-gray-200" />
			<p className="text-xs font-bold text-gray-500 flex items-center mb-2">
				<Package2 size={14} className="mr-1" />
				Detalhes da Caixa
			</p>
			<div className="grid grid-cols-1 md:grid-cols-3 gap-2">
				<div>
					<Label className="text-xs">
						<RequiredLabel>Nome da Caixa</RequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.package.name`}
						control={methodsForm.control}
						rules={{
							required: !isExistingIdPackage ? "Nome da caixa é obrigatório" : false,
						}}
						render={({ field }) => (
							<Input
								type="text"
								placeholder="Ex: Caixa Sabão 12 un."
								readOnly={isExistingIdPackage}
								className={`text-sm ${isExistingIdPackage ? readonlyClass : ""} ${
									methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.name ? "border-red-500" : ""
								}`}
								{...field}
							/>
						)}
					/>
				</div>
				<div>
					<Label className="text-xs flex items-center">
						<RequiredLabel>
							Código de Barras da Caixa
							<div className="relative group inline-block">
								<HelpCircle className="ml-1 w-3 h-3 text-gray-400" />
								<span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 hidden w-max rounded bg-gray-700 p-1 text-xs text-white group-hover:block">
									Código de barras da embalagem maior (caixa).
								</span>
							</div>
						</RequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.package.barcode`}
						control={methodsForm.control}
						rules={{
							required: !isExistingIdPackage ? "Código de barras da caixa é obrigatório" : false,
							pattern: {
								value: /^[0-9]*$/,
								message: "Apenas números são permitidos",
							},
						}}
						render={({ field }) => (
							<Input
								type="string"
								placeholder="Ex: 123456789012345"
								readOnly={isExistingIdPackage}
								className={`text-sm ${isExistingIdPackage ? readonlyClass : ""} ${
									methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.barcode
										? "border-red-500"
										: ""
								}`}
								{...field}
							/>
						)}
					/>
				</div>
				<div>
					<Label className="text-xs">
						<RequiredLabel>Código da Caixa</RequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.package.code`}
						control={methodsForm.control}
						rules={{
							required: !isExistingIdPackage ? "Código da caixa é obrigatório" : false,
						}}
						render={({ field }) => (
							<Input
								type="text"
								placeholder="Ex: CX-0001"
								readOnly={isExistingIdPackage}
								className={`text-sm ${isExistingIdPackage ? readonlyClass : ""} ${
									methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.code ? "border-red-500" : ""
								}`}
								{...field}
							/>
						)}
					/>
				</div>
				<div>
					<Label className="text-xs">
						<RequiredLabel>Itens por Caixa</RequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.package.quantityPerPackage`}
						control={methodsForm.control}
						rules={{
							required: !isExistingIdPackage ? "Quantidade por caixa é obrigatória" : false,
							min: {
								value: 1,
								message: "Quantidade deve ser maior que zero",
							},
						}}
						render={({ field }) => (
							<Input
								type="number"
								inputMode="numeric"
								pattern="[0-9]*"
								placeholder="Ex: 12"
								className={`text-sm no-spinner ${
									methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.quantityPerPackage
										? "border-red-500"
										: ""
								}`}
								{...field}
							/>
						)}
					/>
				</div>
			</div>
			{Object.keys(methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package || {}).length > 0 && (
				<div className="text-center mt-2">
					<span className="text-red-600 text-xs">Preencha todos os campos obrigatórios da caixa</span>
				</div>
			)}
		</div>
	);
};
