import { useCallback } from "react";
import { Control, FieldPath, FieldValues, useController } from "react-hook-form";
import { formatPriceForInput } from "../utils/price-formatter";

interface UsePriceFieldProps<T extends FieldValues> {
	name: FieldPath<T>;
	control: Control<T>;
	rules?: object;
	defaultValue?: number;
}

/**
 * Hook customizado para gerenciar campos de preço com formatação automática
 */
export function usePriceField<T extends FieldValues>({ name, control, rules, defaultValue = 0 }: UsePriceFieldProps<T>) {
	const {
		field: { value, onChange, ...fieldProps },
		fieldState: { error },
	} = useController({
		name,
		control,
		rules,
		defaultValue: defaultValue as any,
	});

	// Converte o valor numérico para string formatada para exibição
	const displayValue = formatPriceForInput(value);

	// Handler para mudanças no input
	const handleChange = useCallback(
		(_formattedValue: string, numericValue: number | undefined) => {
			// Armazena o valor numérico no formulário
			onChange(numericValue ?? 0);
		},
		[onChange]
	);

	// Handler para quando o valor é alterado diretamente (programaticamente)
	const setValue = useCallback(
		(newValue: number) => {
			onChange(newValue);
		},
		[onChange]
	);

	return {
		...fieldProps,
		value: displayValue,
		numericValue: value,
		onChange: handleChange,
		setValue,
		error,
		isValid: !error && value > 0,
	};
}
